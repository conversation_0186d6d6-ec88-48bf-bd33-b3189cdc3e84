import { Injectable } from '@nestjs/common';
import { StorytaleInput } from '../storytale/domain/storytaleInput';
import { OpenAI } from 'openai';
import { AiOutput } from './dto/AiOutput';

@Injectable()
export class AiGeneratorService {
  constructor(private openai: OpenAI) {}
  public async generateStorytale(input: StorytaleInput): Promise<AiOutput> {
    try {
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'user',
            content: `Generate a short story with the following characteristics:
            - **Title:** ${input.title}
            - **Genre:** ${input.genre}
            - **Language:** ${input.language}
            - **Difficulty:** ${input.difficulty} (aim for simple vocabulary and sentence structures).
            
            The story must be **at least 500 words long**.
            
            Format the entire story as an array of arrays. Each inner array should contain two elements: the original sentence in ${input.language} and its direct translation into Spanish.
            
            **Crucially, each sentence (both original and translated) must not exceed four words.**
            **Only the very first word of the original sentence should be capitalized. Subsequent sentences in the story (after the first one) should start with a lowercase letter, even if they begin a new thought or segment.**
            Ensure the story flows naturally despite the short sentence constraint, creating a sense of continuity. Do not include the title.
            
            Respond ONLY with a single-line JSON, no explanation, no markdown, no extra text.
            
            Format: {"story": [["Original sentence 1", "Traducción de la oración 1"], ["original sentence 2", "traducción de la oración 2"], ...]}`,
          },
        ],
        temperature: 0.8, // Increased temperature for more creative flow within constraints
        max_tokens: 3000, // Further increased tokens to handle very short sentences needing more pairs
      });
      const aiResponse = completion.choices[0].message.content;
      console.log('AI response:', aiResponse);
      
      try {
        const jsonContent = JSON.parse(aiResponse) as AiOutput;
        return jsonContent;
      } catch (parseError) {
        console.error('Error parsing AI response:', aiResponse);
        throw new Error(
          'La respuesta de la IA no tiene un formato JSON válido.',
        );
      }
    } catch (error) {
      console.error('Search error:', error.message);
      throw error;
    }
  }
}
