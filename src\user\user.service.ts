import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { UserEntitiy } from './entities/user.entity';
import { CreateUserDto } from './dto/createuser.dto';
import { UpdateUserDto } from './dto/updateuser.dto';
import { FirebaseAdmin } from '../firebase.setup';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(UserEntitiy)
    private readonly userRepository: Repository<UserEntitiy>,
    private readonly firebaseAdmin: FirebaseAdmin,
  ) {}

  findAll() {
    return this.userRepository.find();
  }

  async getUserById(id: string) {
    const user = await this.userRepository.findOneBy({ id });
    if (!user) {
      throw new NotFoundException(`User with id ${id} not found`);
    }
    return user;
  }

  async createUser(payload: CreateUserDto) {
    const user = await this.userRepository.findOneBy({ email: payload.email });
    if (user) {
      throw new ConflictException('User already exists');
    }

    return this.userRepository.save(payload);
  }

  async updateUser(id: string, changes: UpdateUserDto) {
    try {
      const user = await this.getUserById(id);
      if (changes.name) {
        await this.firebaseAdmin
          .setup()
          .auth()
          .updateUser(user.id, { displayName: changes.name });
      }
      this.userRepository.merge(user, changes);
      return this.userRepository.save(user);
    } catch (error) {
      if (error.name === NotFoundException.name) throw error;
      throw new NotFoundException(`Error updating user with id ${id}`);
    }
  }
}
