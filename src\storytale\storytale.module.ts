import { <PERSON>du<PERSON> } from '@nestjs/common';
import { StorytaleController } from './storytale.controller';
import { StorytaleService } from './storytale.service';
import { StorytaleEntity } from './entities/storytale.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AiGeneratorService } from '../ai_generator/ai_generator.service';
import { GenreEntity } from './entities/genre.entitie';

@Module({
  imports: [TypeOrmModule.forFeature([StorytaleEntity, GenreEntity])],
  controllers: [StorytaleController],
  providers: [StorytaleService, AiGeneratorService],
  exports: [StorytaleService],
})
export class StorytaleModule {}
