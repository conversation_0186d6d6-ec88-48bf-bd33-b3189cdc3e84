import { Test, TestingModule } from '@nestjs/testing';
import { UserService } from './user.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { UserEntitiy } from './entities/user.entity';
import { mockUser } from '../testUtils/entities.mock';
import { CreateUserDto } from './dto/createuser.dto';
import { UpdateUserDto } from './dto/updateuser.dto';
import { NotFoundException } from '@nestjs/common';
import { MockFirebaseAdmin } from '../testUtils/services.mock';

describe('UserService', () => {
  let service: UserService;
  const mockDbObject = {
    find: jest.fn(() => []),
    findOneBy: jest.fn(() => mockUser),
    save: jest.fn(),
    merge: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: getRepositoryToken(UserEntitiy),
          useFactory: jest.fn(() => mockDbObject),
        },
        MockFirebaseAdmin,
      ],
    }).compile();

    service = module.get<UserService>(UserService);
  });

  describe('findAll', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });
    it('Should return empty user list when empty db', async () => {
      mockDbObject.find = jest.fn(() => []);
      const result = await service.findAll();
      expect(result.length).toBe(0);
    });

    it('Should return one user when one user on db ', async () => {
      mockDbObject.find = jest.fn(() => [mockUser]);
      const result = await service.findAll();
      expect(result.length).toBe(1);
      expect(result[0].id).toBe(mockUser.id);
    });
  });

  describe('getUserById', () => {
    it('Should return user by id', async () => {
      mockDbObject.findOneBy = jest.fn(() => mockUser);
      const result = await service.getUserById(mockUser.id);
      expect(mockDbObject.findOneBy).toHaveBeenCalled();
      expect(mockDbObject.findOneBy).toHaveBeenCalledTimes(1);
      expect(result.id).toBe(mockUser.id);
    });

    it('Should return error when user not found', async () => {
      const userId = "99";
      mockDbObject.findOneBy = jest.fn().mockResolvedValue(undefined);

      await expect(service.getUserById(userId)).rejects.toThrow(
        `User with id ${userId} not found`,
      );
    });
  });

  describe('createUser', () => {
    it('Should create and return the new user', async () => {
      const payload: CreateUserDto = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: '123456',
        id: "123"
      };
      const savedUser = { id: 1, ...payload };

      mockDbObject.findOneBy = jest.fn().mockResolvedValue(null);

      mockDbObject.save = jest.fn().mockResolvedValue(savedUser);

      const result = await service.createUser(payload);

      expect(mockDbObject.save).toHaveBeenCalledWith(payload);
      expect(mockDbObject.save).toHaveBeenCalledTimes(1);
      expect(result).toEqual(savedUser);
    });

    it('Should throw error when user exists', async () => {
      const payload: CreateUserDto = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: '123456',
        id: "123"
      };
      const existingUser = { id: 1, ...payload };

      mockDbObject.findOneBy = jest.fn().mockResolvedValue(existingUser);

      await expect(service.createUser(payload)).rejects.toThrow(
        'User already exists',
      );

      expect(mockDbObject.findOneBy).toHaveBeenCalledWith({
        email: payload.email,
      });
      expect(mockDbObject.findOneBy).toHaveBeenCalledTimes(1);
    });
  });

  describe('updateUser', () => {
    it('Should throw error if user does not exist', async () => {
      const userId = mockUser.id;
      const payload: UpdateUserDto = { email: '<EMAIL>' };

      service.getUserById = jest
        .fn()
        .mockRejectedValue(
          new NotFoundException(`User with id ${userId} not found`),
        );

      await expect(service.updateUser(userId, payload)).rejects.toThrow(
        `User with id ${userId} not found`,
      );

      expect(service.getUserById).toHaveBeenCalledWith(userId);
      expect(service.getUserById).toHaveBeenCalledTimes(1);
    });

    it('Should update user when user exists', async () => {
      const userId = mockUser.id;
      const changes: UpdateUserDto = { email: '<EMAIL>' };

      const existingUser = {
        id: userId,
        name: 'Old Name',
        email: '<EMAIL>',
        password: 'oldpassword123',
      };

      mockDbObject.merge = jest.fn();
      mockDbObject.save = jest.fn();

      service.getUserById = jest.fn().mockResolvedValue(existingUser);

      await service.updateUser(userId, changes);

      expect(service.getUserById).toHaveBeenCalledWith(userId);
      expect(service.getUserById).toHaveBeenCalledTimes(1);

      expect(mockDbObject.merge).toHaveBeenCalledWith(existingUser, changes);
      expect(mockDbObject.merge).toHaveBeenCalledTimes(1);

      expect(mockDbObject.save).toHaveBeenCalledWith(existingUser);
      expect(mockDbObject.save).toHaveBeenCalledTimes(1);
    });
  });
});
