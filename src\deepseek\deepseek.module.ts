import { Global, Module } from '@nestjs/common';
import OpenAI from 'openai';
import { ConfigType } from '@nestjs/config';
import config from '../config';

@Global()
@Module({
  providers: [
    {
      inject: [config.KEY],
      useFactory: (configServise: ConfigType<typeof config>) => {
        const { apiKey, baseURL } = configServise.deepseek;
        return new OpenAI({
          baseURL,
          apiKey,
        });
      },
      provide: OpenAI,
    },
  ],
  exports: [OpenAI],
})
export class DeepseekModule {}
