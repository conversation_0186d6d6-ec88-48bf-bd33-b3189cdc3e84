import {
  Column,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { StorytaleEntity } from './storytale.entity';
import { Genre } from '../domain/genre';

@Entity('genre')
export class GenreEntity implements Genre {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @OneToMany(() => StorytaleEntity, (storytale) => storytale.genre)
  storytale: StorytaleEntity[];
}
