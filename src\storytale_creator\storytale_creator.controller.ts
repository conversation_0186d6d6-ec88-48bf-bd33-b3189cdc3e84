import { Body, Controller, Post } from '@nestjs/common';
import { StorytaleCreatorService } from './storytale_creator.service';
import { CreateStorytale } from './dto/storytaleCreator.dto';
import { Auth } from '../decorators/auth.decorator';
import { Role } from '../common/enums/role.enum';
import { CurrentUserId } from '../decorators/user.decorator';

@Controller('storytale-creator')
export class StorytaleCreatorController {
  constructor(
    private readonly storytaleCreatorService: StorytaleCreatorService,
  ) {}

  @Post()
  @Auth(Role.USER)
  createStorytale(
    @Body() payload: CreateStorytale,
    @CurrentUserId() userId: string,
  ) {
    return this.storytaleCreatorService.createNewStorytaleForUser(
      userId,
      payload,
    );
  }
}
