import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { UserService } from '../user/user.service';
import { SignUpDto } from './dto/signup.dto';
import { Role } from '../common/enums/role.enum';
import { FirebaseAdmin } from '../firebase.setup';

@Injectable()
export class AuthService {
  constructor(
    private userService: UserService,
    private readonly firebaseAdmin: FirebaseAdmin,
  ) {}

  public async signUp(payload: SignUpDto): Promise<{ email: string; name: string; role: Role }> {
    let createdUserFirebaseId: string | null = null;
    const app = this.firebaseAdmin.setup();

    try {
      const createdUser = await app.auth().createUser({
        email: payload.email,
        password: payload.password,
        displayName: payload.name,
      });
      createdUserFirebaseId = createdUser.uid;
      const userData = await this.userService.createUser({
        ...payload,
        id: createdUser.uid,
      });

      await app.auth().setCustomUserClaims(createdUser.uid, {
        role: Role.USER,
        self_user_id: userData.id,
      });

      return {
        email: userData.email,
        name: userData.name,
        role: userData.role,
      };
    } catch (err) {
      if (createdUserFirebaseId !== null) {
        await app.auth().deleteUser(createdUserFirebaseId);
      }
      throw new HttpException('Error occurred while creating the user', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
