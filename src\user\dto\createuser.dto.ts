import { Is<PERSON><PERSON>, IsS<PERSON>, <PERSON><PERSON>eng<PERSON> } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateUserDto {
   @Transform(({ value }) => value.trim())
   @IsString()
   @MinLength(1)
   name: string;
 
   @IsEmail()
   email: string;
 
   @Transform(({ value }) => value.trim())
   @IsString()
   @MinLength(6)
   password: string;

   @IsString()
   id: string;
}
