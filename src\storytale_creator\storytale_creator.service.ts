import { Injectable } from '@nestjs/common';
import { StorytaleService } from '../storytale/storytale.service';
import { UserService } from '../user/user.service';
import { Storytale } from '../storytale/domain/storytale';
import { MAX_STORYTALE_COUNT_PER_USER } from './configs';
import { StorytaleInput } from '../storytale/domain/storytaleInput';

@Injectable()
export class StorytaleCreatorService {
  constructor(
    private userService: UserService,
    private storytaleService: StorytaleService,
  ) {}

  public async createNewStorytaleForUser(
    userId: string,
    storytaleInput: StorytaleInput,
  ): Promise<Storytale> {
    const user = await this.userService.getUserById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const storyCount =
      await this.storytaleService.findStorytaleCountForUser(userId);

    if (storyCount >= MAX_STORYTALE_COUNT_PER_USER) {
      await this.storytaleService.deleteLastStorytaleForUser(userId);
    }

    return this.storytaleService.createStorytale(userId, storytaleInput);
  }
}
