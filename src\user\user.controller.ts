import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
} from '@nestjs/common';
import { UserService } from './user.service';
import { UpdateUserDto } from './dto/updateuser.dto';
import { CreateUserDto } from './dto/createuser.dto';
import { Auth } from '../decorators/auth.decorator';
import { Role } from '../common/enums/role.enum';

@Controller('users')
export class UsersController {
  constructor(private readonly userService: UserService) {}

  @Get()
  @Auth(Role.ADMIN)
  findAll() {
    return this.userService.findAll();
  }

  @Post()
  @Auth(Role.USER)
  createUser(@Body() payload: CreateUserDto) {
    return this.userService.createUser(payload);
  }

  @Get(':id')
  @Auth(Role.USER)
  getUserById(@Param('id') id: string) {
    return this.userService.getUserById(id);
  }

  @Put(':id')
  @Auth(Role.USER)
  updateUser(
    @Param('id') id: string,
    @Body() payload: UpdateUserDto,
  ) {
    return this.userService.updateUser(id, payload);
  }
}
