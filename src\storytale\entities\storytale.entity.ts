import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  ManyToMany,
  JoinTable,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
} from 'typeorm';
import { Storytale } from '../domain/storytale';
import { UserEntitiy } from '../../user/entities/user.entity';
import { GenreEntity } from '../../storytale/entities/genre.entitie';
import { TranslatedPhrasesListTransformer } from '../storytale.mapper';
import { TranslatedPhrase } from '../domain/translatedPhrase';

@Entity('storytales')
export class StorytaleEntity implements Storytale {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  userId: string;

  @Column()
  title: string;

  @Column({
    type: 'text',
  })
  content: string;

  @Column()
  language: string;

  @Column()
  difficulty: string;

  @Column()
  isDefault: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @ManyToOne(() => GenreEntity, (genre) => genre.storytale)
  genre: GenreEntity;

  @ManyToMany(() => UserEntitiy, (user) => user.storytales)
  @JoinTable({ name: 'storytale_user' })
  users: UserEntitiy[];

  @Column({
    type: 'text',
    transformer: new TranslatedPhrasesListTransformer(),
    name: 'translated_phrases',
  })
  translatedPhrases: TranslatedPhrase[];
}
