import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';

import { StorytaleService } from './storytale.service';
import {
  mockGenres,
  mockStorytale,
  mockStorytaleInput,
  mockStorytales,
  mockUser,
} from '../testUtils/entities.mock';
import { AiGeneratorService } from '../ai_generator/ai_generator.service';
import { StorytaleEntity } from './entities/storytale.entity';
import {
  MAX_STORYTALE_CONTENT_LENGTH,
  MIN_STORYTALE_CONTENT_LENGTH,
} from './configs';
import { Storytale } from './domain/storytale';
import { GenreEntity } from './entities/genre.entitie';

describe('StorytaleService', () => {
  let service: StorytaleService;
  let aiService: AiGeneratorService;
  let repositorySaveFunction: jest.Mock;
  let repositoryFindFunction: jest.Mock;
  let repositoryDeleteFunction: jest.Mock;
  let repositoryCountFunction: jest.Mock;
  let repositoryGetAllByIdFunction: jest.Mock;
  let repositoryFindGenreFunction: jest.Mock;
  const mockFindOneGenre = jest.fn(() => mockGenres[0]);

  beforeEach(async () => {
    repositorySaveFunction = jest.fn(() => mockStorytale);
    repositoryFindFunction = jest.fn(() => mockStorytale);
    repositoryDeleteFunction = jest.fn(() => undefined);
    repositoryCountFunction = jest.fn(() => 5);
    repositoryGetAllByIdFunction = jest.fn(() => []);
    repositoryFindGenreFunction = jest.fn(() => []);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StorytaleService,
        {
          provide: AiGeneratorService,
          useValue: {},
        },
        {
          provide: getRepositoryToken(StorytaleEntity),
          useValue: {
            save: repositorySaveFunction,
            findOne: repositoryFindFunction,
            delete: repositoryDeleteFunction,
            count: repositoryCountFunction,
            find: repositoryGetAllByIdFunction,
          },
        },
        {
          provide: getRepositoryToken(GenreEntity),
          useValue: {
            find: repositoryFindGenreFunction,
            findOne: mockFindOneGenre, // <-- Agregado para soportar findOne
          },
        },
      ],
    }).compile();

    service = module.get<StorytaleService>(StorytaleService);
    aiService = module.get<AiGeneratorService>(AiGeneratorService);

    aiService.generateStorytale = jest.fn(() =>
      Promise.resolve({ story: mockStorytale.content, phrases: [] }),
    );
  });

  it('create new storytale should call ai generator', async () => {
    await service.createStorytale(mockUser.id, mockStorytaleInput);
    expect(aiService.generateStorytale).toHaveBeenCalled();
    expect(aiService.generateStorytale).toHaveBeenCalledWith({
      ...mockStorytaleInput,
      genre: 'Fantasía',
    });
  });

  it('create new storytale with the generated text on the db', async () => {
    const storytale = await service.createStorytale(
      mockUser.id,
      mockStorytaleInput,
    );
    expect(storytale.content).toEqual(mockStorytale.content);
    expect(repositorySaveFunction).toHaveBeenCalledTimes(1);
    expect(storytale.userId).toBe(mockUser.id);
    expect(repositorySaveFunction).toHaveBeenCalledWith({
      content: storytale.content,
      genre: expect.anything(), // Ahora puede ser un objeto GenreEntity
      difficulty: mockStorytaleInput.difficulty,
      language: mockStorytaleInput.language,
      title: mockStorytaleInput.title,
      isDefault: false,
      translatedPhrases: [],
      userId: mockUser.id,
    });
  });

  it('throw exception if generated storytale is too short', async () => {
    const shortText = new Array(MIN_STORYTALE_CONTENT_LENGTH - 1)
      .fill('a')
      .join('');
    aiService.generateStorytale = jest.fn(() =>
      Promise.resolve({
        story: shortText,
        phrases: [],
      }),
    );
    await expect(
      service.createStorytale(mockUser.id, mockStorytaleInput),
    ).rejects.toThrow();
  });

  it('throw exception if generated storytale is too large', async () => {
    const largeText = new Array(MAX_STORYTALE_CONTENT_LENGTH + 1)
      .fill('a')
      .join('');
    aiService.generateStorytale = jest.fn(() =>
      Promise.resolve({
        story: largeText,
        phrases: [],
      }),
    );
    await expect(
      service.createStorytale(mockUser.id, mockStorytaleInput),
    ).rejects.toThrow();
  });

  describe('getAllStorytalesForUser', () => {
    it('Should return an empty array if the user has no stories', async () => {
      repositoryGetAllByIdFunction.mockResolvedValue([]);

      const result = await service.getAllStorytalesForUser('1');

      expect(result).toEqual([]);
    });

    it('Should return the stories if they exist in the database', async () => {
      const storytales: Storytale[] = mockStorytales;

      repositoryGetAllByIdFunction.mockResolvedValue(storytales);

      const result = await service.getAllStorytalesForUser('1');
      expect(result).toEqual(storytales);
    });
  });

  describe('findStorytaleCountForUser', () => {
    it('should return the number of Storytales for a user', async () => {
      const userId = mockUser.id;
      const count = 5;

      repositoryCountFunction.mockResolvedValue(count);

      const result = await service.findStorytaleCountForUser(userId);

      expect(result).toBe(count);
      expect(repositoryCountFunction).toHaveBeenCalledWith({
        where: { userId },
      });
      expect(repositoryCountFunction).toHaveBeenCalledTimes(1);
    });

    it('should return 0 if the user has no Storytales', async () => {
      const userId = mockUser.id;
      const count = 0; // No hay historias

      repositoryCountFunction.mockResolvedValue(count);

      const result = await service.findStorytaleCountForUser(userId);

      expect(result).toBe(0);
      expect(repositoryCountFunction).toHaveBeenCalledWith({
        where: { userId },
      });
    });
  });

  describe('getStorytaleById', () => {
    it('should return the Storytale by id', async () => {
      const id = mockStorytale.id;
      const storytale = await service.getStorytaleById(id);

      expect(storytale).toEqual(mockStorytale);
      expect(repositoryFindFunction).toHaveBeenCalledWith({ where: { id } });
    });

    it('should throw an exception if Storytale is not found', async () => {
      repositoryFindFunction.mockResolvedValue(null);

      const id = 999;

      await expect(service.getStorytaleById(id)).rejects.toThrow(
        `Storytale with id ${id} not found`,
      );
    });
  });

  describe('deleteStorytaleById', () => {
    it('should call delete function if the Storytale exists', async () => {
      const id = mockStorytale.id;

      await service.deleteStorytaleById(id);

      expect(repositoryDeleteFunction).toHaveBeenCalledWith({ id });
      expect(repositoryDeleteFunction).toHaveBeenCalledTimes(1);
    });

    it('should throw an exception if Storytale is not found', async () => {
      repositoryFindFunction.mockResolvedValue(null);

      const id = 999;

      await expect(service.deleteStorytaleById(id)).rejects.toThrow(
        `Storytale with id ${id} not found`,
      );
    });
  });

  describe('getGenres', () => {
    it('Should return the list of genres', async () => {
      repositoryFindGenreFunction.mockResolvedValue(mockGenres);

      const result = await service.getGenres();

      expect(repositoryFindGenreFunction).toHaveBeenCalledTimes(1);
      expect(repositoryFindGenreFunction).toHaveBeenCalledWith();
      expect(result).toEqual(mockGenres);
    });

    it('Should return an empty array if there are no genres', async () => {
      repositoryFindGenreFunction.mockResolvedValue([]);

      const result = await service.getGenres();

      expect(repositoryFindGenreFunction).toHaveBeenCalledTimes(1);
      expect(repositoryFindGenreFunction).toHaveBeenCalledWith();
      expect(result).toEqual([]);
    });
  });

  describe('deleteLastStorytaleForUser', () => {
    it('should delete the last storytale for a user', async () => {
      const userId = '1';
      const lastStorytale = { id: 123, userId };

      // Mock findOne para devolver un storytale
      repositoryFindFunction.mockResolvedValue(lastStorytale);

      // Ejecuta el método
      await service.deleteLastStorytaleForUser(userId);

      expect(repositoryFindFunction).toHaveBeenCalledWith({
        where: { userId },
        order: { id: 'ASC' }, // Debe ser ASC para el más antiguo
      });
      expect(repositoryDeleteFunction).toHaveBeenCalledWith({
        id: lastStorytale.id,
      });
    });

    it('should throw NotFoundException if user has no storytales', async () => {
      const userId = '1';
      repositoryFindFunction.mockResolvedValue(null);

      await expect(service.deleteLastStorytaleForUser(userId)).rejects.toThrow(
        'No storytales found for user',
      );
    });
  });
});
