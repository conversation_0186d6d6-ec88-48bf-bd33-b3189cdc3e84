# Story Application Backend

![NestJS](https://img.shields.io/badge/NestJS-10.0.0-E0234E?style=flat-square&logo=nestjs)
![TypeScript](https://img.shields.io/badge/TypeScript-5.1.3-3178C6?style=flat-square&logo=typescript)
![MySQL](https://img.shields.io/badge/MySQL-8.0-4479A1?style=flat-square&logo=mysql)
![Firebase](https://img.shields.io/badge/Firebase-13.1.0-FFCA28?style=flat-square&logo=firebase)
![OpenAI](https://img.shields.io/badge/OpenAI-4.84.0-412991?style=flat-square&logo=openai)

Una aplicación backend para la generación y gestión de cuentos interactivos para el aprendizaje de idiomas, construida con NestJS, TypeORM, Firebase Authentication y OpenAI.

## 📋 Descripción

Story Application Backend es una API RESTful que permite a los usuarios crear, gestionar y acceder a cuentos generados por IA. La aplicación está diseñada para ayudar a los estudiantes de idiomas a mejorar su comprensión mediante cuentos personalizados con frases traducidas destacadas.

### Características principales

- **Autenticación de usuarios** con Firebase
- **Generación de cuentos** utilizando IA (OpenAI/Deepseek)
- **Gestión de cuentos** por usuario
- **Categorización por géneros** y niveles de dificultad
- **Frases traducidas** para facilitar el aprendizaje de idiomas

## 🚀 Tecnologías

- **Framework**: [NestJS](https://nestjs.com/)
- **Base de datos**: MySQL con TypeORM
- **Autenticación**: Firebase Authentication
- **Generación de contenido**: OpenAI/Deepseek API
- **Contenedor**: Docker y Docker Compose

## 🛠️ Estructura del proyecto

```
src/
├── ai_generator/         # Servicio de generación de cuentos con IA
├── auth/                 # Autenticación y autorización
├── common/               # Utilidades y enums compartidos
├── database/             # Configuración de la base de datos
├── deepseek/             # Integración con Deepseek/OpenAI
├── firebase/             # Configuración de Firebase
├── guards/               # Guards de autenticación
├── storytale/            # Gestión de cuentos
├── storytale_creator/    # Creación de cuentos
├── user/                 # Gestión de usuarios
├── app.module.ts         # Módulo principal
└── main.ts               # Punto de entrada
```

## 📦 Instalación

### Prerrequisitos

- Node.js (v18.x recomendado)
- MySQL (o Docker para usar con docker-compose)
- Cuenta de Firebase con Authentication habilitado
- Clave API de OpenAI o Deepseek

### Configuración del entorno

1. Clona el repositorio:
   ```bash
   git clone https://github.com/tu-usuario/story_application_backend.git
   cd story_application_backend
   ```

2. Instala las dependencias:
   ```bash
   npm install
   ```

3. Crea un archivo `.env` en la raíz del proyecto con la siguiente estructura:
   ```
   # Desarrollo (MySQL)
   NODE_ENV=development

   # Base de datos
   MYSQL_DATABASE=story_application_dev
   MYSQL_PORT=3307
   MYSQL_ROOT_PASSWORD=tu_contraseña
   MYSQL_USER=root
   MYSQL_HOST=localhost
   MYSQL_AUTHENTICATION_PLUGIN=mysql_native_password
   PMA_HOST=mysql

   # Deepseek/OpenAI
   DEEPSEEK_API_KEY=tu_api_key
   DEEPSEEK_BASE_URL=https://api.openai.com/v1

   # Firebase Service Account
   FIREBASE_TYPE=service_account
   FIREBASE_PROJECT_ID=tu_proyecto_id
   FIREBASE_PRIVATE_KEY_ID=tu_private_key_id
   FIREBASE_PRIVATE_KEY="tu_private_key"
   FIREBASE_CLIENT_EMAIL=tu_client_email
   FIREBASE_CLIENT_ID=tu_client_id
   FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
   FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
   FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
   FIREBASE_CLIENT_X509_CERT_URL=tu_client_x509_cert_url
   FIREBASE_UNIVERSE_DOMAIN=googleapis.com

   # Puerto del servidor
   PORT=3000
   ```

> ⚠️ **Importante**: Nunca subas el archivo `.env` o las credenciales de Firebase al control de versiones. Asegúrate de que estén incluidos en el `.gitignore`.

## 🏃‍♂️ Ejecución

### Desarrollo

```bash
# Modo desarrollo
npm run start:dev

# Modo producción
npm run start:prod
```

### Docker

```bash
# Iniciar servicios con Docker Compose
docker-compose up -d
```

## 🧪 Pruebas

```bash
# Pruebas unitarias
npm run test

# Pruebas e2e
npm run test:e2e

# Cobertura de pruebas
npm run test:cov
```

## 📚 API Endpoints

### Autenticación

- `POST /api/auth/signup` - Registro de usuario

### Usuarios

- `GET /api/users` - Obtener todos los usuarios
- `GET /api/users/:id` - Obtener usuario por ID
- `POST /api/users` - Crear usuario
- `PUT /api/users/:id` - Actualizar usuario

### Cuentos

- `GET /api/storytale/count/:userId` - Obtener cantidad de cuentos por usuario
- `GET /api/storytale/storytales/user/:userId` - Obtener todos los cuentos de un usuario
- `GET /api/storytale/genres` - Obtener todos los géneros
- `GET /api/storytale/:id` - Obtener cuento por ID
- `DELETE /api/storytale/:id` - Eliminar cuento

### Creación de cuentos

- `POST /api/storytale-creator` - Crear nuevo cuento

## 🔒 Seguridad

La aplicación utiliza Firebase Authentication para la gestión de usuarios y autenticación. Las credenciales de Firebase se configuran a través de variables de entorno para mayor seguridad.

### Roles de usuario

- `USER` - Usuario estándar
- `ADMIN` - Administrador (funcionalidades adicionales)

## 🤝 Contribución

1. Haz un fork del repositorio
2. Crea una rama para tu feature (`git checkout -b feature/amazing-feature`)
3. Haz commit de tus cambios (`git commit -m 'Add some amazing feature'`)
4. Haz push a la rama (`git push origin feature/amazing-feature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Consulta el archivo `LICENSE` para más detalles.
