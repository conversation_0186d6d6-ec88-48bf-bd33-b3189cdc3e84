import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { UserModule } from './user/user.module';
import { StorytaleModule } from './storytale/storytale.module';
import { DatabaseModule } from './database/database.module';

import config from './config';
import { enviroments } from './enviroments';
import { StorytaleCreatorModule } from './storytale_creator/storytale_creator.module';
import { AiGeneratorModule } from './ai_generator/ai_generator.module';
import { FirebaseModule } from './firebase/firebase.module';
import { DeepseekModule } from './deepseek/deepseek.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: enviroments[process.env.NODE_ENV] || '.env',
      load: [config],
      isGlobal: true,
    }),
    DatabaseModule,
    UserModule,
    StorytaleModule,
    StorytaleCreatorModule,
    AuthModule,
    AiGeneratorModule,
    FirebaseModule,
    DeepseekModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
