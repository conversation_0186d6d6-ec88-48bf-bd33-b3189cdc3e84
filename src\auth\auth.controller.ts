import {
  Body,
  Controller,
  HttpException,
  Post,
  UnauthorizedException,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { SignUpDto } from './dto/signup.dto';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('signup')
  public async signUp(@Body() registerDto: SignUpDto) {
    try {
      const result = await this.authService.signUp(registerDto);
      if (result) {
        return result;
      } else {
        throw new UnauthorizedException();
      }
    } catch (error) {
      throw new HttpException(error.message, error.status);
    }
  }
}
