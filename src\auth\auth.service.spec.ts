import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { MockFirebaseAdmin, MockUserService } from '../testUtils/services.mock';
import { UserService } from '../user/user.service';
import { mockUser, signUpDto } from '../testUtils/entities.mock';
import { FirebaseAdmin } from '../firebase.setup';
import { UserEntitiy } from '../user/entities/user.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Role } from '../common/enums/role.enum';
import { HttpException, HttpStatus } from '@nestjs/common';

describe('AuthService', () => {
  let service: AuthService;
  let userService: UserService;
  let firebaseAdmin: FirebaseAdmin;
  const firebaseDeleteUserMock: jest.Mock = jest.fn(() => Promise.resolve());

  beforeEach(async () => {
    jest.resetAllMocks();
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        MockUserService,
        MockFirebaseAdmin,
        {
          provide: getRepositoryToken(UserEntitiy),
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userService = module.get<UserService>(UserService);
    userService.createUser = jest.fn(() => Promise.resolve(mockUser));
    firebaseAdmin = module.get<FirebaseAdmin>(FirebaseAdmin);
    (firebaseAdmin as any).setup = jest.fn(() => ({
      auth: () => ({
        createUser: jest.fn(() => Promise.resolve({ uid: '123' })),
        setCustomUserClaims: jest.fn(() => Promise.resolve()),
        deleteUser: firebaseDeleteUserMock,
      }),
    }));
  });

  it('should call user service create user on signup', async () => {
    await service.signUp(signUpDto);
    expect(userService.createUser as jest.Mock).toHaveBeenCalledTimes(1);
  });

  it('should call user service with USER role', async () => {
    await service.signUp(signUpDto);
    expect(userService.createUser as jest.Mock).toHaveBeenCalledWith({
      ...signUpDto,
      id: expect.any(String),
    });
  });

  it('should return user data when sign up is successful', async () => {
    const userData = {
      email: '<EMAIL>',
      name: 'Test User',
      role: Role.USER,
    };

    userService.createUser = jest.fn().mockResolvedValue(userData); // Mock para createUser

    const result = await service.signUp(signUpDto);

    expect(result).toEqual({
      email: '<EMAIL>',
      name: 'Test User',
      role: Role.USER,
    });
  });

  it('should throw an error if create user fails', async () => {
    userService.createUser = jest
      .fn()
      .mockRejectedValue(new Error('Failed to create user'));

    await expect(service.signUp(signUpDto)).rejects.toThrowError(
      new HttpException(
        'Error occurred while creating the user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      ),
    );
  });

  it('should delete firebase user if it was created but then an error occurs', async () => {
    userService.createUser = jest.fn(() => Promise.reject());
  
    await expect(service.signUp(signUpDto)).rejects.toThrow(
      new HttpException('Error occurred while creating the user', HttpStatus.INTERNAL_SERVER_ERROR),
    );
  
    expect(firebaseDeleteUserMock).toHaveBeenCalledTimes(1);
  });
});
