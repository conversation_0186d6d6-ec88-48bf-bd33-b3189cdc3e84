import { Module } from '@nestjs/common';
import { UserService } from './user.service';
import { UsersController } from './user.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserEntitiy } from './entities/user.entity';
import { StorytaleEntity } from '../storytale/entities/storytale.entity';
import { FirebaseModule } from '../firebase/firebase.module';

@Module({
  imports: [TypeOrmModule.forFeature([UserEntitiy, StorytaleEntity])],
  providers: [UserService],
  controllers: [UsersController],
  exports: [UserService, TypeOrmModule.forFeature([UserEntitiy, StorytaleEntity])],
})
export class UserModule {}
