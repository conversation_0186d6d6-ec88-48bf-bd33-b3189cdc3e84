import { StorytaleInput } from '../storytale/domain/storytaleInput';
import { Role } from '../common/enums/role.enum';
import { UserEntitiy } from '../user/entities/user.entity';
import { Storytale } from '../storytale/domain/storytale';
import { MIN_STORYTALE_CONTENT_LENGTH } from '../storytale/configs';
import { Genre } from '../storytale/domain/genre';

export const mockUser: UserEntitiy = {
  id: "1",
  name: 'Mock',
  email: '<EMAIL>',
  password: 'asdasdgsasf',
  role: Role.USER,
  createdAt: new Date(),
  updatedAt: null,
  storytales: [],
};

export const mockGenre: Genre = {
  id: 1,
  name: 'Fantas<PERSON>',
  storytale: [],
  createdAt: new Date('2024-01-01T12:00:00Z'),
  updatedAt: new Date('2024-01-02T12:00:00Z'),
};

export const mockStorytale: Storytale = {
  userId: "1",
  id: 1,
  title: 'string',
  content: new Array(MIN_STORYTALE_CONTENT_LENGTH + 1).fill('a').join(''),
  genre: mockGenre, 
  language: 'string',
  difficulty: 'string',
  isDefault: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  users: [],
  translatedPhrases: [],
};

export const mockStorytales: Storytale[] = [
  {
    userId: "1",
    id: 1,
    title: 'Cuento de Aventuras',
    content: 'Érase una vez...',
    genre: mockGenre,
    language: 'Español',
    difficulty: 'Fácil',
    isDefault: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-10'),
    users: [{ id: "1", name: 'Juan', email: '<EMAIL>' } as UserEntitiy],
    translatedPhrases: [],
  },
  {
    userId: "1",
    id: 2,
    title: 'Cuento de Terror',
    content: 'La noche era oscura...',
    genre: mockGenre,
    language: 'Español',
    difficulty: 'Difícil',
    isDefault: true,
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-02-15'),
    users: [{ id: "1", name: 'Juan', email: '<EMAIL>' } as UserEntitiy],
    translatedPhrases: [],
  },
];

export const mockStorytaleInput: StorytaleInput = {
  title: 'title',
  genre: 'genre',
  language: 'es',
  difficulty: 'easy',
};

export const signUpDto = {
  email: '<EMAIL>',
  password: 'password',
  name: 'test',
};

export const mockGenres: Genre[] = [
  {
    id: 1,
    name: 'Fantasía',
    storytale: [],
    createdAt: new Date('2024-01-01T12:00:00Z'),
    updatedAt: new Date('2024-01-02T12:00:00Z'),
  },
  {
    id: 2,
    name: 'Ciencia Ficción',
    storytale: [],
    createdAt: new Date('2024-02-01T12:00:00Z'),
    updatedAt: new Date('2024-02-02T12:00:00Z'),
  },
];
