import { Injectable, NotFoundException } from '@nestjs/common';
import { Storytale } from './domain/storytale';
import { StorytaleInput } from './domain/storytaleInput';
import { AiGeneratorService } from '../ai_generator/ai_generator.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { StorytaleEntity } from './entities/storytale.entity';
import {
  MAX_STORYTALE_CONTENT_LENGTH,
  MIN_STORYTALE_CONTENT_LENGTH,
} from './configs';
import { GenreEntity } from './entities/genre.entitie';
import { Genre } from './domain/genre';
import { getTranslatedPhrase } from './storytale.mapper';

@Injectable()
export class StorytaleService {
  constructor(
    @InjectRepository(StorytaleEntity)
    private readonly storytaleRepository: Repository<StorytaleEntity>,
    @InjectRepository(GenreEntity)
    private readonly genreRepository: Repository<GenreEntity>,
    private aiGeneratorService: AiGeneratorService,
  ) {}

  public async createStorytale(
    userId: string,
    storytaleInput: StorytaleInput,
  ): Promise<Storytale> {
    // Buscar la entidad de género usando el id recibido
    const genreEntity = await this.genreRepository.findOne({
      where: { id: Number(storytaleInput.genre) },
    });

    if (!genreEntity) {
      throw new NotFoundException(
        `Genre with id ${storytaleInput.genre} not found`,
      );
    }

    const aiInput: StorytaleInput = {
      ...storytaleInput,
      genre: genreEntity.name,
    };

    const generatedContent =
      await this.aiGeneratorService.generateStorytale(aiInput);

    if (generatedContent.story.length < MIN_STORYTALE_CONTENT_LENGTH) {
      throw new Error('Generated story is too short');
    }

    if (generatedContent.story.length > MAX_STORYTALE_CONTENT_LENGTH) {
      throw new Error('Generated story is too long');
    }

    const createdStorytale = await this.storytaleRepository.save({
      content: generatedContent.story,
      genre: genreEntity,
      difficulty: storytaleInput.difficulty,
      language: storytaleInput.language,
      title: storytaleInput.title,
      isDefault: false,
      userId,
      translatedPhrases: getTranslatedPhrase(generatedContent),
    });

    return createdStorytale;
  }

  async getAllStorytalesForUser(userId: string): Promise<Storytale[]> {
    const stories = await this.storytaleRepository.find({
      where: { userId },
      relations: ['genre'],
    });
    return stories;
  }

  public findStorytaleCountForUser(userId: string): Promise<number> {
    return this.storytaleRepository.count({ where: { userId } });
  }

  public async getStorytaleById(id: number): Promise<Storytale> {
    const storytale = await this.storytaleRepository.findOne({ where: { id } });

    if (!storytale) {
      throw new NotFoundException(`Storytale with id ${id} not found`);
    }

    return storytale;
  }

  public async deleteStorytaleById(id: number): Promise<void> {
    await this.getStorytaleById(id);
    await this.storytaleRepository.delete({ id });
  }

  public async deleteLastStorytaleForUser(userId: string): Promise<void> {
    // Busca el último storytale creado por el usuario (orden descendente por id)
    const lastStorytale = await this.storytaleRepository.findOne({
      where: { userId },
      order: { id: 'ASC' },
    });

    if (!lastStorytale) {
      throw new NotFoundException(`No storytales found for user ${userId}`);
    }

    await this.storytaleRepository.delete({ id: lastStorytale.id });
  }

  public async getGenres(): Promise<Genre[]> {
    return this.genreRepository.find();
  }
}
