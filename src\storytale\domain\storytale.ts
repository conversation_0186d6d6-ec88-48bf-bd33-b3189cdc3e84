import { UserEntitiy } from '../../user/entities/user.entity';
import { GenreEntity } from '../entities/genre.entitie';
import { TranslatedPhrase } from './translatedPhrase';

export class Storytale {
  userId: string;
  id: number;
  title: string;
  content: string;
  genre: GenreEntity;
  language: string;
  difficulty: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
  users: UserEntitiy[];
  translatedPhrases: TranslatedPhrase[];
}
