import OpenAI from 'openai';
import { AiGeneratorService } from '../ai_generator/ai_generator.service';
import { FirebaseAdmin } from '../firebase.setup';
import { StorytaleService } from '../storytale/storytale.service';
import { UserService } from '../user/user.service';

export const MockUserService = {
  provide: UserService,
  useFactory: () => ({}),
};

export const MockStorytaleService = {
  provide: StorytaleService,
  useFactory: () => ({}),
};

export const MockAiGeneratorService = {
  provide: AiGeneratorService,
  useFactory: () => ({}),
};

export const MockFirebaseAdmin = {
  provide: FirebaseAdmin,
  useFactory: () => ({}),
};

export const MockOpenAi = {
  provide: OpenAI,
  useFactory: () => ({}),
};
