import { Test, TestingModule } from '@nestjs/testing';
import { AiGeneratorService } from './ai_generator.service';
import { MockOpenAi } from '../testUtils/services.mock';

describe('AiGeneratorService', () => {
  let service: AiGeneratorService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AiGeneratorService, MockOpenAi],
    }).compile();

    service = module.get<AiGeneratorService>(AiGeneratorService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
