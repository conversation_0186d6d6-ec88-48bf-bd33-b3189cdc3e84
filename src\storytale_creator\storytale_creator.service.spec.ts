import { Test, TestingModule } from '@nestjs/testing';
import { StorytaleCreatorService } from './storytale_creator.service';
import {
  MockStorytaleService,
  MockUserService,
} from '../testUtils/services.mock';
import { UserService } from '../user/user.service';
import { StorytaleService } from '../storytale/storytale.service';
import {
  mockStorytale,
  mockStorytaleInput,
  mockUser,
} from '../testUtils/entities.mock';
import { MAX_STORYTALE_COUNT_PER_USER } from './configs';

describe('StorytaleCreatorService', () => {
  let service: StorytaleCreatorService;
  let userService: UserService;
  let storytaleService: StorytaleService;

  const userId = "1";

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StorytaleCreatorService,
        MockUserService,
        MockStorytaleService,
      ],
    }).compile();

    service = module.get<StorytaleCreatorService>(StorytaleCreatorService);
    userService = module.get<UserService>(UserService);
    storytaleService = module.get<StorytaleService>(StorytaleService);

    storytaleService.createStorytale = jest.fn(() =>
      Promise.resolve(mockStorytale),
    );

    storytaleService.findStorytaleCountForUser = jest.fn(() =>
      Promise.resolve(0),
    );

    userService.getUserById = jest.fn(() => Promise.resolve(mockUser));
  });

  it('create new storytale for user should call storytale service', async () => {
    await service.createNewStorytaleForUser(userId, mockStorytaleInput);

    expect(storytaleService.createStorytale as jest.Mock).toHaveBeenCalled();
  });

  it('create new storytale for user should return new storytale', async () => {
    const newStorytale = await service.createNewStorytaleForUser(
      userId,
      mockStorytaleInput,
    );
    
    expect(newStorytale).toBeDefined();
    expect(newStorytale.id).toBe(mockStorytale.id);
  });

  it('should fetch user data before creating a new storytale', async () => {
    await service.createNewStorytaleForUser(userId, mockStorytaleInput);

    expect(userService.getUserById).toHaveBeenCalled();
  });

  it('should throw an error if user does not exist', async () => {
    userService.getUserById = jest.fn(() => Promise.resolve(null));

    await expect(
      service.createNewStorytaleForUser(userId, mockStorytaleInput),
    ).rejects.toThrow();
  });

  it('should call findStorytaleCountForUser', async () => {
    await service.createNewStorytaleForUser(userId, mockStorytaleInput);
    expect(storytaleService.findStorytaleCountForUser).toHaveBeenCalled();
  });

 it('should delete last storytale and create a new one if user reached max storytales', async () => {
  storytaleService.findStorytaleCountForUser = jest.fn(() =>
    Promise.resolve(MAX_STORYTALE_COUNT_PER_USER),
  );
  storytaleService.deleteLastStorytaleForUser = jest.fn(() => Promise.resolve());

  await service.createNewStorytaleForUser(userId, mockStorytaleInput);

  expect(storytaleService.deleteLastStorytaleForUser).toHaveBeenCalledWith(userId);
  expect(storytaleService.createStorytale).toHaveBeenCalledWith(userId, mockStorytaleInput);
});
});
