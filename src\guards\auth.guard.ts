import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { FirebaseAdmin } from '../firebase.setup';
import { Role } from '../common/enums/role.enum';
import { DecodedIdToken } from 'firebase-admin/lib/auth/token-verifier';

@Injectable()
export class AuthGuard implements CanActivate {
  private cachedTokens: { [key: string]: DecodedIdToken } = {};
  constructor(
    private reflector: Reflector,
    private readonly admin: FirebaseAdmin,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const app = this.admin.setup();
    const idToken = context.getArgs()[0]?.headers?.authorization.split(' ')[1];

    const permissions = this.reflector.get<Role[]>(
      'permissions',
      context.getHandler(),
    );
    try {
      let claims: DecodedIdToken;
      if (this.cachedTokens[idToken]) {
        claims = this.cachedTokens[idToken];
      } else {
        claims = await app.auth().verifyIdToken(idToken);
        this.cachedTokens[idToken] = claims;

        setTimeout(
          () => {
            delete this.cachedTokens[idToken];
          },
          Date.now() - (claims.exp - 10) * 1000,
        );
      }

      const request = context.switchToHttp().getRequest();
      request.userId = claims.self_user_id;

      if (claims.role === Role.ADMIN) {
        return true;
      }
      if (permissions.map((r) => r.toString()).includes(claims.role)) {
        return true;
      }

      throw {
        message: 'You do not have permission',
      };
    } catch (error) {
      throw new UnauthorizedException(
        error.message ? error.message : 'Token is invalid',
      );
    }
  }
}
