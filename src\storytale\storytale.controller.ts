import { Controller, Delete, Get, Param, ParseIntPipe } from '@nestjs/common';
import { StorytaleService } from './storytale.service';
import { Auth } from '../decorators/auth.decorator';
import { Role } from '../common/enums/role.enum';

@Controller('storytale')
export class StorytaleController {
  constructor(private readonly storytaleService: StorytaleService) {}

  @Get('count/:userId')
  @Auth(Role.USER)
  getStorytaleCountForUser(@Param('userId') userId: string) {
    return this.storytaleService.findStorytaleCountForUser(userId);
  }

  @Get('storytales/user/:userId')
  @Auth(Role.USER)
  getAllStorytalesForUser(@Param('userId') userId: string) {
    return this.storytaleService.getAllStorytalesForUser(userId);
  }

  @Get('genres')
  @Auth(Role.USER)
  getAllGenres() {
    return this.storytaleService.getGenres();
  }

  @Get(':id')
  @Auth(Role.USER)
  getStorytaleById(@Param('id', ParseIntPipe) id: number) {
    return this.storytaleService.getStorytaleById(id);
  }

  @Delete(':id')
  @Auth(Role.USER)
  deleteStorytaleById(@Param('id', ParseIntPipe) id: number) {
    return this.storytaleService.deleteStorytaleById(id);
  }
}
