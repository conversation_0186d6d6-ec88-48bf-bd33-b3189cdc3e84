packages/*/package-lock.json
sample/*/package-lock.json

# dependencies
node_modules/

# IDE
/.idea
/.awcache
/.vscode
/.devcontainer
/.classpath
/.project
/.settings
*.code-workspace

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]

# bundle
packages/**/*.d.ts
packages/**/*.js

# misc
.DS_Store
lerna-debug.log
npm-debug.log
yarn-error.log
/**/npm-debug.log
/packages/**/.npmignore
/packages/**/LICENSE
*.tsbuildinfo

# example
/quick-start
/example_dist
/example

# tests
/test
/benchmarks/memory
/coverage
/.nyc_output
/packages/graphql
/benchmarks/memory
build/config\.gypi

.npmrc
pnpm-lock.yaml
/.history

.env
.env.local
.prod.env

/mysql_data
/dist
/src/firebaseServiceAccountKey.json