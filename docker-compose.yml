services:
  mysql:
      image: mysql:8
      environment:
        - MYSQL_DATABASE=${MYSQL_DATABASE}
        - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
        - MYSQL_AUTHENTICATION_PLUGIN=${MYSQL_AUTHENTICATION_PLUGIN}
      ports:
        - '${MYSQL_PORT}:3306'
      volumes:
        - ./mysql_data:/var/lib/mysql

  phpmyadmin:
      image: phpmyadmin/phpmyadmin
      environment:
        - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
        - PMA_HOST=${PMA_HOST}
      ports:
        - '8080:80'
      depends_on:
        - mysql