import { ValueTransformer } from 'typeorm';
import { TranslatedPhrase } from './domain/translatedPhrase';
import { AiOutput } from '../ai_generator/dto/AiOutput';

export class TranslatedPhrasesListTransformer implements ValueTransformer {
  to(value: TranslatedPhrase[]): string {
    return value.map((v) => JSON.stringify(v)).join('|');
  }

  from(translatedPhrases: string): TranslatedPhrase[] {
    const rawPhreases = translatedPhrases.split('|');
    return rawPhreases.map((raw) => JSON.parse(raw));
  }
}

export function getTranslatedPhrase(aiOutput: AiOutput) {
  return aiOutput.phrases.map((p) => {
    const [original, translated] = p.split(' // ');
    return {
      original,
      translated,
    };
  });
}
