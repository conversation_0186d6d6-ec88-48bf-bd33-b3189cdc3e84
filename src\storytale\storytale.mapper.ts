import { ValueTransformer } from 'typeorm';
import { TranslatedPhrase } from './domain/translatedPhrase';
import { AiOutput } from '../ai_generator/dto/AiOutput';

export class TranslatedPhrasesListTransformer implements ValueTransformer {
  to(value: TranslatedPhrase[]): string {
    return value.map((v) => JSON.stringify(v)).join('|');
  }

  from(translatedPhrases: string): TranslatedPhrase[] {
    const rawPhreases = translatedPhrases.split('|');
    return rawPhreases.map((raw) => JSON.parse(raw));
  }
}

export function getTranslatedPhrase(aiOutput: AiOutput) {
  // Tomar las primeras 8 frases del array de arrays
  return aiOutput.story
    .slice(0, 8)
    .map(([original, translated]) => ({
      original,
      translated,
    }));
}
